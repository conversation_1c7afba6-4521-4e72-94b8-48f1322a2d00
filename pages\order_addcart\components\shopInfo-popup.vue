<template>
    <tui-bottom-popup :show="show" @close="handleClose" :z-index="1000" :height="557 * 2">
        <view class="shopInfo-popup">
            <!-- 头部显示商品信息 -->
            <view class="shopInfo-popup-header">
                <view class="picture-wrapper">
                    <image src="https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg"
                        mode="widthFix"></image>
                </view>
                <view class="good-info-wrapper">
                    <view>
                        <text class="price-symbol">￥</text>
                        <text class="price-value">164.00</text>
                    </view>
                    <view class="good-info-wrapper-bottom">
                        <text class="price-confirm">已选</text>
                        <text class="price-name">原味燕窝胶原饮90g</text>
                    </view>
                </view>
            </view>
            <!-- 商品列表展示部分 -->
            <view class="shopInfo-popup-list">
                <view class="showInfo-popup-list-header">
                    <text>规格</text>
                    <view class="showInfo-popup-list-header-right">
                        <image src="/static/images/order-addcart/list.svg" mode="widthFix"></image>
                        <text class="list-text">列表</text>
                    </view>
                </view>
                <view class="list-content">
                    <view class="list-content-item" v-for="(item, index) in list" :key="item.id"
                        :class="{ active: activeIndex === index }" @click="handleClick(index)">
                        <view class="list-content-item-top">
                            <image src="https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg"
                                mode="widthFix"></image>
                        </view>
                        <text class="list-content-item-bottom">
                            原味燕窝胶原饮90g
                        </text>
                    </view>
                </view>
            </view>
        </view>
    </tui-bottom-popup>
</template>

<script>
import tuiBottomPopup from '@/components/base/tui-bottom-popup.vue';
export default {
    components: {
        tuiBottomPopup
    },
    emits: ['close'],
    data() {
        return {
            list: [
                {
                    id: 1,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 2,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                }
            ],
            activeIndex: 0
        }
    },
    props: {
        shopInfo: {
            type: Object,
            default: () => { }
        },
        show: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        handleClose() {
            this.$emit('close');
        },
        handleClick(index) {
            this.activeIndex = index;
        }
    }
}
</script>

<style lang="scss" scoped>
@import "../index.scss";

.shopInfo-popup {
    z-index: 1000;
    height: 100%;
    padding-top: 60rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
}

// header
.shopInfo-popup-header {
    display: flex;
    align-content: center;
    gap: 14rpx;
    margin-bottom: 46rpx;
}

.good-info-wrapper {
    align-self: flex-end;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .price-confirm {
        color: #666;
        font-size: 24rpx;
        font-weight: 400;
    }

    .price-symbol {
        color: #ff0000;
        font-size: 28rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .price-value {
        color: #ff0000;
        font-size: 40rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .good-info-wrapper-bottom {
        display: flex;
        gap: 12rpx;
        align-items: center;
    }
}

// 商品列表展示部分
.shopInfo-popup-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

.showInfo-popup-list-header {
    display: flex;
    justify-content: space-between;

    text:nth-child(1) {
        color: #131313;
        font-size: 28rpx;
        font-weight: 500;
    }

    .showInfo-popup-list-header-right {
        display: flex;
        align-content: center;
        gap: 8rpx;

        image {
            width: 32rpx;
            height: 32rpx;
        }

        text {
            color: #131313;
            font-size: 24rpx;
            font-weight: 400;
        }
    }
}

.list-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    // flex-direction: column;
    gap: 20rpx;
    border-radius: 16rpx;
}

.list-content-item {
    display: flex;
    flex-direction: column;
    gap: 7rpx;

    // background-color: #f9f2f2;
    border-radius: 16rpx;
    min-width: 0;

    &.active {
        border: 2px solid #F00;

        .list-content-item-bottom {
            color: #F00;
        }
    }

    .list-content-item-top {
        border-radius: 16rpx;
        overflow: hidden;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .list-content-item-bottom {
        color: #131313;
        font-size: 24rpx;
        font-weight: 400;

    }
}
</style>